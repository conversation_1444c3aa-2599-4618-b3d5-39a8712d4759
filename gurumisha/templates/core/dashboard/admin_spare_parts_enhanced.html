{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block page_title %}Enhanced Spare Parts Management{% endblock %}
{% block page_description %}Comprehensive spare parts inventory management with advanced features{% endblock %}

{% block dashboard_content %}
<div class="space-y-8">
    <!-- Enhanced Header with Actions -->
    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 animate-fade-in-up" style="animation-delay: 0.1s;">
        <div>
            <h2 class="text-3xl font-bold text-harrier-dark font-montserrat">Enhanced Spare Parts Management</h2>
            <p class="text-gray-600 mt-1 font-raleway">Complete inventory management with real-time analytics and M-Pesa integration</p>
        </div>
        
        <div class="flex flex-wrap gap-3">
            <button onclick="openAddPartModal()" class="enhanced-btn enhanced-btn-primary text-sm">
                <i class="fas fa-plus mr-2"></i>Add Spare Part
            </button>
            <button onclick="openBulkImportModal()" class="enhanced-btn enhanced-btn-secondary text-sm">
                <i class="fas fa-upload mr-2"></i>Bulk Import
            </button>
            <button onclick="exportSparePartsData()" class="enhanced-btn enhanced-btn-secondary text-sm">
                <i class="fas fa-download mr-2"></i>Export Data
            </button>
            <button onclick="openInventoryAlertsModal()" class="enhanced-btn enhanced-btn-secondary text-sm relative">
                <i class="fas fa-exclamation-triangle mr-2"></i>Alerts
                {% if low_stock_parts > 0 %}
                <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {{ low_stock_parts }}
                </span>
                {% endif %}
            </button>
        </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate-fade-in-up" style="animation-delay: 0.2s;">
        <div class="admin-stat-card group hover:scale-105 transition-transform duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <div class="admin-stat-value text-blue-600">{{ total_parts|default:0 }}</div>
                    <div class="admin-stat-label">Total Parts</div>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                    <i class="fas fa-cogs text-blue-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 text-sm text-gray-600">
                <span class="text-green-600">+12%</span> from last month
            </div>
        </div>

        <div class="admin-stat-card group hover:scale-105 transition-transform duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <div class="admin-stat-value text-green-600">{{ in_stock_parts|default:0 }}</div>
                    <div class="admin-stat-label">In Stock</div>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center group-hover:bg-green-200 transition-colors">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 text-sm text-gray-600">
                <span class="text-green-600">{{ in_stock_parts|floatformat:0 }}%</span> availability
            </div>
        </div>

        <div class="admin-stat-card group hover:scale-105 transition-transform duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <div class="admin-stat-value text-orange-600">{{ low_stock_parts|default:0 }}</div>
                    <div class="admin-stat-label">Low Stock</div>
                </div>
                <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center group-hover:bg-orange-200 transition-colors">
                    <i class="fas fa-exclamation-triangle text-orange-600 text-xl"></i>
                </div>
            </div>
            <div class="mt-4 text-sm text-gray-600">
                Requires attention
            </div>
        </div>

        <div class="admin-stat-card group hover:scale-105 transition-transform duration-300">
            <div class="flex items-center justify-between">
                <div>
                    <div class="admin-stat-value text-harrier-red">KSh {{ inventory_value|floatformat:0 }}</div>
                    <div class="admin-stat-label">Inventory Value</div>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center group-hover:bg-red-200 transition-colors">
                    <i class="fas fa-dollar-sign text-harrier-red text-xl"></i>
                </div>
            </div>
            <div class="mt-4 text-sm text-gray-600">
                Total stock value
            </div>
        </div>
    </div>

    <!-- Advanced Filters and Search -->
    <div class="admin-card animate-fade-in-up" style="animation-delay: 0.3s;">
        <div class="p-6">
            <form method="GET" class="space-y-4" id="filter-form"
                  hx-get="{% url 'core:admin_spare_parts_enhanced' %}"
                  hx-target="#spare-parts-table"
                  hx-trigger="change, submit"
                  hx-indicator="#loading-indicator">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700">Search Parts</label>
                        <div class="relative">
                            <input type="text" name="search" value="{{ search_query }}"
                                   placeholder="Name, SKU, Part Number..."
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent"
                                   hx-get="{% url 'core:admin_spare_parts_enhanced' %}"
                                   hx-target="#spare-parts-table"
                                   hx-trigger="keyup changed delay:500ms"
                                   hx-indicator="#loading-indicator">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>

                    <!-- Category Filter -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700">Category</label>
                        <select name="category" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if current_category == category.id|stringformat:"s" %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Supplier Filter -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700">Supplier</label>
                        <select name="supplier" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent">
                            <option value="">All Suppliers</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}" {% if current_supplier == supplier.id|stringformat:"s" %}selected{% endif %}>
                                {{ supplier.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700">Stock Status</label>
                        <select name="status" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent">
                            <option value="">All Status</option>
                            <option value="in_stock" {% if current_status == 'in_stock' %}selected{% endif %}>In Stock</option>
                            <option value="low_stock" {% if current_status == 'low_stock' %}selected{% endif %}>Low Stock</option>
                            <option value="out_of_stock" {% if current_status == 'out_of_stock' %}selected{% endif %}>Out of Stock</option>
                        </select>
                    </div>
                </div>

                <div class="flex justify-between items-center pt-4 border-t border-gray-200">
                    <div class="text-sm text-gray-600">
                        Showing {{ spare_parts|length }} of {{ total_parts }} parts
                    </div>
                    <div class="flex space-x-3">
                        <button type="button" onclick="clearFilters()" class="enhanced-btn enhanced-btn-secondary text-sm">
                            <i class="fas fa-times mr-2"></i>Clear Filters
                        </button>
                        <button type="submit" class="enhanced-btn enhanced-btn-primary text-sm">
                            <i class="fas fa-filter mr-2"></i>Apply Filters
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Enhanced Spare Parts Table -->
    <div class="admin-card animate-fade-in-up" style="animation-delay: 0.4s;">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-cogs text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Spare Parts Inventory</h3>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Loading Indicator -->
                    <div id="loading-indicator" class="htmx-indicator">
                        <div class="flex items-center space-x-2 text-harrier-red">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-harrier-red"></div>
                            <span class="text-sm">Loading...</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <label class="text-sm text-gray-600">View:</label>
                        <button onclick="toggleView('table')" id="table-view-btn" class="px-3 py-1 text-sm rounded-lg bg-harrier-red text-white">
                            <i class="fas fa-table mr-1"></i>Table
                        </button>
                        <button onclick="toggleView('grid')" id="grid-view-btn" class="px-3 py-1 text-sm rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50">
                            <i class="fas fa-th mr-1"></i>Grid
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Table View -->
        <div id="table-view" class="overflow-x-auto">
            <div id="spare-parts-table">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" id="select-all" class="rounded border-gray-300 text-harrier-red focus:ring-harrier-red">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                            Part Details
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                            Category & Supplier
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                            Stock Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                            Pricing
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="parts-table-body">
                    {% for part in spare_parts %}
                    <tr class="hover:bg-gray-50 transition-colors duration-200 group">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="part-checkbox rounded border-gray-300 text-harrier-red focus:ring-harrier-red" value="{{ part.id }}">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                {% if part.main_image %}
                                    <img src="{{ part.main_image.url }}" alt="{{ part.name }}" 
                                         class="w-12 h-12 object-cover rounded-lg border-2 border-gray-200 mr-4 group-hover:border-harrier-red transition-colors">
                                {% else %}
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-200 to-blue-300 rounded-lg flex items-center justify-center mr-4">
                                        <i class="fas fa-cogs text-blue-600 text-lg"></i>
                                    </div>
                                {% endif %}
                                <div>
                                    <div class="text-sm font-medium text-harrier-dark font-raleway">{{ part.name }}</div>
                                    <div class="text-sm text-gray-500">SKU: {{ part.sku }}</div>
                                    {% if part.part_number %}
                                    <div class="text-xs text-gray-600">Part #: {{ part.part_number }}</div>
                                    {% endif %}
                                    {% if part.barcode %}
                                    <div class="text-xs text-gray-600">
                                        <i class="fas fa-barcode mr-1"></i>{{ part.barcode }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="space-y-1">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ part.category_new.name|default:part.category|default:"Uncategorized" }}
                                </span>
                                {% if part.supplier %}
                                <div class="text-sm text-gray-600">{{ part.supplier.name }}</div>
                                {% else %}
                                <div class="text-sm text-gray-400">No supplier</div>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                {% if part.stock_quantity > part.minimum_stock %}
                                    <div class="w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                                    <span class="text-sm text-green-800 font-medium">In Stock</span>
                                {% elif part.stock_quantity > 0 %}
                                    <div class="w-3 h-3 bg-orange-400 rounded-full mr-2 animate-pulse"></div>
                                    <span class="text-sm text-orange-800 font-medium">Low Stock</span>
                                {% else %}
                                    <div class="w-3 h-3 bg-red-400 rounded-full mr-2 animate-pulse"></div>
                                    <span class="text-sm text-red-800 font-medium">Out of Stock</span>
                                {% endif %}
                            </div>
                            <div class="text-xs text-gray-500 mt-1">{{ part.stock_quantity }} / {{ part.minimum_stock }} min</div>
                            {% if part.stock_quantity <= part.reorder_point %}
                            <div class="text-xs text-orange-600 font-medium mt-1">
                                <i class="fas fa-exclamation-triangle mr-1"></i>Reorder needed
                            </div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="space-y-1">
                                <div class="text-sm font-semibold text-harrier-dark">KSh {{ part.price|floatformat:0 }}</div>
                                {% if part.cost_price %}
                                <div class="text-xs text-gray-500">Cost: KSh {{ part.cost_price|floatformat:0 }}</div>
                                {% endif %}
                                {% if part.discount_price %}
                                <div class="text-xs text-green-600">Sale: KSh {{ part.discount_price|floatformat:0 }}</div>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <button onclick="viewPartDetail({{ part.id }})" 
                                        class="text-blue-600 hover:text-blue-800 transition-colors p-1 rounded hover:bg-blue-50" 
                                        title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button onclick="editPart({{ part.id }})" 
                                        class="text-green-600 hover:text-green-800 transition-colors p-1 rounded hover:bg-green-50" 
                                        title="Edit Part">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="updateStock({{ part.id }})" 
                                        class="text-purple-600 hover:text-purple-800 transition-colors p-1 rounded hover:bg-purple-50" 
                                        title="Update Stock">
                                    <i class="fas fa-plus-circle"></i>
                                </button>
                                <button onclick="deletePart({{ part.id }})" 
                                        class="text-red-600 hover:text-red-800 transition-colors p-1 rounded hover:bg-red-50" 
                                        title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-12 text-center">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-cogs text-gray-400 text-2xl"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">No spare parts found</h4>
                            <p class="text-gray-600 font-raleway mb-4">No spare parts match your current filters.</p>
                            <button onclick="clearFilters()" class="enhanced-btn enhanced-btn-primary">
                                <i class="fas fa-times mr-2"></i>Clear Filters
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            </div>
        </div>

        <!-- Pagination -->
        {% if spare_parts.has_other_pages %}
        <div class="px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    Showing {{ spare_parts.start_index }} to {{ spare_parts.end_index }} of {{ spare_parts.paginator.count }} results
                </div>
                <div class="flex space-x-2">
                    {% if spare_parts.has_previous %}
                    <a href="?page={{ spare_parts.previous_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50">
                        <i class="fas fa-chevron-left mr-1"></i>Previous
                    </a>
                    {% endif %}
                    
                    <span class="px-3 py-2 text-sm bg-harrier-red text-white rounded-lg">
                        Page {{ spare_parts.number }} of {{ spare_parts.paginator.num_pages }}
                    </span>
                    
                    {% if spare_parts.has_next %}
                    <a href="?page={{ spare_parts.next_page_number }}" class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50">
                        Next<i class="fas fa-chevron-right ml-1"></i>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* Enhanced animations and transitions */
    .admin-stat-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .admin-stat-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    /* Table row animations */
    tbody tr {
        transition: all 0.2s ease;
    }

    tbody tr:hover {
        transform: translateX(2px);
    }

    /* Loading states */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Pulse animation for stock indicators */
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    /* Custom scrollbar for table */
    .overflow-x-auto::-webkit-scrollbar {
        height: 8px;
    }

    .overflow-x-auto::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .overflow-x-auto::-webkit-scrollbar-thumb {
        background: #DC2626;
        border-radius: 4px;
    }

    .overflow-x-auto::-webkit-scrollbar-thumb:hover {
        background: #B91C1C;
    }

    /* Enhanced Button Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.25rem;
        font-weight: 600;
        font-size: 0.875rem;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        font-family: 'Montserrat', sans-serif;
        border: 2px solid transparent;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .enhanced-btn-primary {
        background: linear-gradient(135deg,
            #DC2626 0%,
            #1F2937 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    }

    .enhanced-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(220, 38, 38, 0.4);
    }

    .enhanced-btn-secondary {
        background: rgba(255, 255, 255, 0.9);
        color: #1F2937;
        border-color: rgba(156, 163, 175, 0.3);
        backdrop-filter: blur(10px);
    }

    .enhanced-btn-secondary:hover {
        background: rgba(255, 255, 255, 1);
        border-color: #DC2626;
        color: #DC2626;
        transform: translateY(-1px);
    }

    /* HTMX Loading Indicator */
    .htmx-indicator {
        opacity: 0;
        transition: opacity 200ms ease-in;
    }

    .htmx-request .htmx-indicator {
        opacity: 1;
    }

    .htmx-request.htmx-indicator {
        opacity: 1;
    }
</style>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
// Enhanced Spare Parts Management JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeSparePartsManagement();
});

function initializeSparePartsManagement() {
    // Initialize select all checkbox
    initializeSelectAll();

    // Initialize real-time search
    initializeRealTimeSearch();

    // Initialize tooltips
    initializeTooltips();
}

function initializeSelectAll() {
    const selectAllCheckbox = document.getElementById('select-all');
    const partCheckboxes = document.querySelectorAll('.part-checkbox');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            partCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }

    partCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateBulkActions();
        });
    });
}

function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('select-all');
    const partCheckboxes = document.querySelectorAll('.part-checkbox');
    const checkedBoxes = document.querySelectorAll('.part-checkbox:checked');

    if (selectAllCheckbox) {
        selectAllCheckbox.checked = checkedBoxes.length === partCheckboxes.length;
        selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < partCheckboxes.length;
    }
}

function updateBulkActions() {
    const checkedBoxes = document.querySelectorAll('.part-checkbox:checked');
    // Show/hide bulk action buttons based on selection
    // Implementation depends on your bulk action UI
}

function clearFilters() {
    const form = document.getElementById('filter-form');
    if (form) {
        form.reset();
        form.submit();
    }
}

function toggleView(viewType) {
    const tableView = document.getElementById('table-view');
    const gridView = document.getElementById('grid-view');
    const tableBtn = document.getElementById('table-view-btn');
    const gridBtn = document.getElementById('grid-view-btn');

    if (viewType === 'table') {
        tableView.style.display = 'block';
        if (gridView) gridView.style.display = 'none';

        tableBtn.classList.add('bg-harrier-red', 'text-white');
        tableBtn.classList.remove('border', 'border-gray-300', 'text-gray-600');

        gridBtn.classList.remove('bg-harrier-red', 'text-white');
        gridBtn.classList.add('border', 'border-gray-300', 'text-gray-600');
    } else {
        if (gridView) gridView.style.display = 'block';
        tableView.style.display = 'none';

        gridBtn.classList.add('bg-harrier-red', 'text-white');
        gridBtn.classList.remove('border', 'border-gray-300', 'text-gray-600');

        tableBtn.classList.remove('bg-harrier-red', 'text-white');
        tableBtn.classList.add('border', 'border-gray-300', 'text-gray-600');
    }

    // Save preference
    localStorage.setItem('spare_parts_view', viewType);
}

// Modal Functions
function openAddPartModal() {
    showToast('Add Part Modal - Implementation needed', 'info');
}

function openBulkImportModal() {
    showToast('Bulk Import Modal - Implementation needed', 'info');
}

function openInventoryAlertsModal() {
    showToast('Inventory Alerts Modal - Implementation needed', 'info');
}

// Part Management Functions
function viewPartDetail(partId) {
    fetch(`/dashboard/admin/spare-parts/view/${partId}/modal/`)
        .then(response => response.text())
        .then(html => {
            document.body.insertAdjacentHTML('beforeend', html);
        })
        .catch(error => {
            console.error('Error loading modal:', error);
            showToast('Error loading part details', 'error');
        });
}

function editPart(partId) {
    fetch(`/dashboard/admin/spare-parts/edit/${partId}/modal/`)
        .then(response => response.text())
        .then(html => {
            document.body.insertAdjacentHTML('beforeend', html);
        })
        .catch(error => {
            console.error('Error loading modal:', error);
            showToast('Error loading edit part modal', 'error');
        });
}

function updateStock(partId) {
    fetch(`/dashboard/admin/spare-parts/restock/${partId}/modal/`)
        .then(response => response.text())
        .then(html => {
            document.body.insertAdjacentHTML('beforeend', html);
        })
        .catch(error => {
            console.error('Error loading modal:', error);
            showToast('Error loading restock modal', 'error');
        });
}

function deletePart(partId) {
    if (confirm('Are you sure you want to delete this spare part? This action cannot be undone.')) {
        fetch(`/dashboard/admin/spare-parts/delete/${partId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'HX-Request': 'true'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success !== false) {
                showToast('Spare part deleted successfully', 'success');
                // Refresh the table
                location.reload();
            } else {
                showToast(data.error || 'Error deleting spare part', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Error deleting spare part', 'error');
        });
    }
}

function exportSparePartsData() {
    showLoading('Preparing export...');

    const form = document.getElementById('filter-form');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);

    window.location.href = `/admin/spare-parts/export/?${params.toString()}`;

    hideLoading();
    showToast('Export started. Download will begin shortly.', 'success');
}

function showLoading(message = 'Loading...') {
    const overlay = document.createElement('div');
    overlay.id = 'loading-overlay';
    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    overlay.innerHTML = `
        <div class="bg-white rounded-lg p-6 text-center">
            <i class="fas fa-spinner fa-spin text-harrier-red text-3xl mb-4"></i>
            <p class="text-harrier-dark font-semibold">${message}</p>
        </div>
    `;
    document.body.appendChild(overlay);
}

function hideLoading() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) overlay.remove();
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    const bgColor = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'warning': 'bg-orange-500',
        'info': 'bg-blue-500'
    }[type] || 'bg-blue-500';

    toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-semibold ${bgColor}`;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

function initializeTooltips() {
    // Simple tooltip implementation for action buttons
    const tooltipElements = document.querySelectorAll('[title]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            const tooltip = document.createElement('div');
            tooltip.className = 'absolute bg-gray-800 text-white text-xs rounded px-2 py-1 z-50';
            tooltip.textContent = this.getAttribute('title');
            this.parentNode.appendChild(tooltip);
            this.tooltipElement = tooltip;
        });

        element.addEventListener('mouseleave', function() {
            if (this.tooltipElement) {
                this.tooltipElement.remove();
                this.tooltipElement = null;
            }
        });
    });
}

// Load saved view preference
document.addEventListener('DOMContentLoaded', function() {
    const savedView = localStorage.getItem('spare_parts_view') || 'table';
    toggleView(savedView);
});
</script>
{% endblock %}
